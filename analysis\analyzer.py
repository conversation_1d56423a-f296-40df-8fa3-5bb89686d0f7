import os
import openseespy.opensees as ops
import numpy as np
import matplotlib.pyplot as plt
from gm.read_record import ReadRecord

from utils.bearing_axial_load import update_bearing_materials

# Import summarize functions
from analysis.summarize import (
    summarize_bearing_relative_displacements as summarize_bearing_disps,
    summarize_pier as summarize_pier,
    summarize_abutment_displacements as summarize_abutment_disps,
    summarize_deck_displacements as summarize_deck_disps
)

# Import accident checking functions
from analysis.check_accidents import check_girder_falling, check_bearing_failure

# Import recorder functions
from analysis.recorder import (
    setup_pier_recorders,
    setup_abutment_recorders,
    setup_deck_recorders,
    setup_bearing_recorders,
    read_abutment_displacement_data,
    read_deck_displacement_data,
    remove_recorders
)

from analysis.recorder.bearing import find_bearings_closest_to_y0


class BridgeAnalyzer:
    def __init__(self, model):
        self.model = model
        self.frequencies = []      # 固有频率存储(Hz)
        self.bearing_relative_disps = {}  # 支座相对位移数据存储
        self.recorder_info = {}           # 记录器信息存储
        self.pier_displacement = False    # 桥墩位移超限事故标志
        self._init_analysis()


    def _init_analysis(self):
        """初始化分析基本参数"""
        self.damping_ratio = self.model.params.damping_ratio

        # seismic analysis
        self.dt, self.npts = None, None
        self.direction = 1 # 纵向施加地震荷载
        # self.direction = 2 # 横向施加地震荷载
        self.gm_time_series_tag = 2
        self.seismic_pattern_tag = 2


    def _setup_modal_analysis(self):
        """模态分析配置 - 使用一致质量矩阵"""
        ops.wipeAnalysis()
        ops.system('FullGeneral')  # 特征值分析需要完全存储

        # 基本分析设置
        ops.numberer('RCM')
        ops.constraints('Transformation')

        # 设置质量矩阵形式 - 使用一致质量矩阵
        ops.algorithm('Linear')

        # 使用一致质量矩阵进行模态分析
        # GimmeMCK参数: 1.0表示提取质量矩阵, 0.0表示不提取阻尼和刚度矩阵
        # 第四个参数为1表示使用一致质量矩阵而不是集中质量矩阵
        try:
            ops.integrator('GimmeMCK', 1.0, 0.0, 0.0, 1)  # 生成一致质量矩阵
        except:
            ops.integrator('GimmeMCK', 1.0, 0.0, 0.0)  # 生成集中质量矩阵

        ops.analysis('Transient')  # 伪时程分析设置


    def _setup_static_analysis(self):
        """静力分析配置"""
        ops.wipeAnalysis()
        ops.system('BandGeneral')
        ops.constraints('Transformation')
        ops.numberer('RCM')
        ops.test('NormDispIncr', 1.0e-6, 10)
        ops.algorithm('Newton')
        ops.integrator('LoadControl', 0.1)
        ops.analysis('Static')


    def _setup_dynamic_analysis(self):
        """非线性动力时程配置"""
        ops.wipeAnalysis()
        ops.system('BandGeneral')
        ops.numberer('RCM')
        ops.constraints('Transformation')
        ops.test('NormDispIncr', 1e-4, 100)
        # ops.algorithm('Newton')
        ops.algorithm('KrylovNewton')
        ops.integrator('Newmark', 0.5, 0.25)
        # ops.integrator('HHT', 0.7)
        ops.analysis('Transient')


    def modal(self, num_modes=3):
        """执行模态分析"""
        self._setup_modal_analysis()

        # 执行特征值分析
        eigen_values = ops.eigen(num_modes)

        # 计算并存储频率
        self.frequencies = self._calc_frequencies(eigen_values)

        # 设置瑞利阻尼
        self._set_rayleigh()


    def static(self, update_bearing=False):
        """执行静力分析"""
        self._setup_static_analysis()
        self._perform_static_analysis()

        # 计算节点反力
        ops.reactions()

        # 更新支座材料属性
        if update_bearing:
            update_bearing_materials(self.model)


    def _perform_static_analysis(self):
        """执行静力分析并记录支座轴向荷载"""
        # 执行分析
        try:
            ops.analyze(10)
            # print("静力分析完成")
        except Exception as e:
            print(f"静力分析失败: {e}")
            return


    def dynamic(self, h='gm/elCentro.at2', pga=0.15, dt=0.01):
        """执行动力时程分析 - 使用固定时间步长"""
        # 静力分析, 记录支座轴向荷载
        if not hasattr(self.model, 'bearing_axial_loads'):
            self.static(update_bearing=True)
        ops.reset()
        print("静力分析完成")

        # 地震动力分析
        ops.loadConst('-time', 0.0)
        self._setup_dynamic_analysis()
        self._apply_ground_motion(h, pga, dt)

        # 设置响应记录器 - 记录桥墩、桥台、主梁节点和支座
        self.recorder_info = setup_pier_recorders(self.model, self.dt, recorder_info=None)
        self.recorder_info = setup_abutment_recorders(self.model, self.dt, recorder_info=self.recorder_info)
        self.recorder_info = setup_deck_recorders(self.model, self.dt, recorder_info=self.recorder_info)
        self.recorder_info = setup_bearing_recorders(self.model, self.dt, recorder_info=self.recorder_info)

        current_time = 0.0
        target_time = self.dt * self.npts

        # 使用固定时间步长
        dt_fixed = self.dt  # 使用地震记录的固定时间步长
        # dt_fixed = 0.01

        print(f"\n开始地震动力分析, 目标时间 {target_time:.2f}s, 固定时间步长 {dt_fixed:.3f}s...")

        # 记录分析状态
        self.analysis_stats = {
            'total_steps': 0,
            'successful_steps': 0,
            'algorithm_switches': 0,
            'skipped_steps': 0
        }

        step = 0
        while current_time < target_time - 1e-10:
            step += 1
            success = False

            # 首先尝试使用当前设置分析, 如果失败, 尝试其他算法
            try:
                if ops.analyze(1, dt_fixed) == 0:
                    success = True
            except Exception:
                pass

            if not success:
                print(f"\n时间步 {step} (t={current_time:.3f}s) 初始分析失败, 尝试不同算法...")
                success = self._try_different_algorithms(dt_fixed)
                if success:
                    self.analysis_stats['algorithm_switches'] += 1

            # 如果所有算法都失败, 跳过当前步
            if not success:
                print(f"\n警告: 时间 {current_time:.3f}s 无法收敛, 跳过当前步")
                current_time += dt_fixed
                self.analysis_stats['skipped_steps'] += 1
                continue

            # 成功分析当前步
            if success:
                ops.reactions()  # 计算反力
                self._record_responses(current_time)  # 记录数据

                current_time += dt_fixed
                self.analysis_stats['successful_steps'] += 1

            self.analysis_stats['total_steps'] += 1
            self._setup_dynamic_analysis()

            # 每10步显示一次进度
            if (self.analysis_stats['total_steps'] % 10 == 0) or (abs(current_time-target_time) < 1e-10):
                progress = current_time / target_time * 100
                print(f"\r地震分析进度: {current_time:.3f}/{target_time:.3f}s ({progress:.1f}%)", end="")

        print(f"\n地震动力分析完成 {current_time:.3f}/{target_time:.3f}s")
        print(f"总步数: {self.analysis_stats['total_steps']}")
        print(f"成功步数: {self.analysis_stats['successful_steps']}")
        print(f"算法切换次数: {self.analysis_stats['algorithm_switches']}")
        print(f"跳过步数: {self.analysis_stats['skipped_steps']}")

        # 输出各组件相对位移数据摘要
        print("\n"+"="*60)
        print("响应数据处理\n"+"="*60)
        # 支座相对位移
        summarize_bearing_disps(self.bearing_relative_disps)

        # 桥墩相对位移 - 使用记录器数据
        if self.model.piers['nodes']:
            # 使用记录器数据并检查桥墩位移超限事故
            self.pier_displacement = summarize_pier(self.recorder_info, model=self.model)

        # 桥台节点位移 - 使用记录器数据
        abutment_disps = read_abutment_displacement_data(self.recorder_info)
        summarize_abutment_disps(abutment_disps)

        # 主梁节点位移 - 使用记录器数据
        deck_disps = read_deck_displacement_data(self.recorder_info)
        summarize_deck_disps(deck_disps)

        print("\n"+"="*60)
        print("事故检查\n"+"="*60)
        # 检查是否发生落梁事故
        self.check_accidents()

        # 移除记录器
        remove_recorders(self.recorder_info)

        ops.wipe()

        # 返回分析统计信息
        return self.analysis_stats


    def check_accidents(self):
        """检查是否发生事故（落梁、支座剪切破坏）

        桥墩位移超限事故检查已经在summarize_pier函数中完成
        """
        # 检查落梁事故
        girder_falling = check_girder_falling(
            self.bearing_relative_disps, model=self.model, params=self.model.params)

        # 检查支座剪切破坏事故
        bearing_failure = check_bearing_failure(
            self.bearing_relative_disps, model=self.model, params=self.model.params)

        # 桥墩位移超限事故检查结果已经在self.pier_displacement中
        return girder_falling or self.pier_displacement or bearing_failure


    def _calc_frequencies(self, eigen_values):
        """将特征值转换为频率(Hz)"""
        freqs = [np.sqrt(abs(value))/(2*np.pi) for value in eigen_values if value > 0]
        for i, freq in enumerate(freqs[:3]):
            print(f"Modal Frequency {i+1}: {freq:.2f} Hz")
        return freqs


    def _set_rayleigh(self):
        """设置瑞利阻尼"""
        omega1 = 2 * np.pi * self.frequencies[0]  # 第一模态频率
        omega2 = 2 * np.pi * self.frequencies[1]  # 第二模态频率
        # 计算瑞利阻尼系数
        xi = self.damping_ratio
        alpha = (2 * xi * omega1 * omega2) / (omega1 + omega2)
        beta = (2 * xi) / (omega1 + omega2)

        ops.rayleigh(alpha, beta, 0.0, 0.0)


    def _apply_ground_motion(self, h='gm/elCentro.at2', pga=0.15, dt=0.01):
        """施加地震动

        参数:
            h: 地震记录, 支持PEER格式文件路径(.at2)和Python/numpy数组
        """
        # 确保结果目录存在
        os.makedirs('gm', exist_ok=True)
        os.makedirs('results', exist_ok=True)

        # 检查文件类型
        if isinstance(h, str):
            if h.lower().endswith('.at2'):
                # 读取PEER格式地震波, 生成中间数据文件
                temp_file = 'gm/temp.dat'
                self.dt, self.npts = ReadRecord(h, temp_file)

                # 定义时间序列
                ops.timeSeries(
                    'Path', self.gm_time_series_tag,
                    '-dt', self.dt,
                    '-time', list(np.arange(0, self.npts*self.dt, self.dt)),
                    '-filePath', temp_file,
                    '-factor', self.model.params.gravity / 0.348 * pga
                )
        else:
            # 处理Python/numpy数组
            if not isinstance(h, np.ndarray):
                h = np.array(h)
            
            self.dt = dt
            h = h / np.abs(h).max() * pga
            h[0] = 0  # 确保起始加速度为0

            # 限制记录长度，避免过长的分析时间
            # max_points = 9000
            # if len(h) > max_points:
            #     h = h[:max_points]

            # 保存处理后的数据
            temp_file = 'gm/temp.dat'
            np.savetxt(temp_file, h)

            # 绘制地震波形
            plt.figure(figsize=(12, 3))
            plt.plot(np.arange(0, len(h)*self.dt, self.dt)[:len(h)], h)
            plt.xlabel('Time (s)')
            plt.ylabel('Acceleration (g)')
            plt.grid(True)
            plt.savefig('gm/earthquake_record.png', dpi=300)
            plt.close()
            self.npts = len(h)

            # 定义时间序列
            ops.timeSeries(
                'Path', self.gm_time_series_tag,
                '-dt', self.dt,
                '-time', list(np.arange(0, self.npts*self.dt, self.dt)),
                '-filePath', temp_file,
                '-factor', self.model.params.gravity
            )

        # 定义荷载模式
        ops.pattern('UniformExcitation',
                    self.seismic_pattern_tag,
                    self.direction,
                    '-accel', self.gm_time_series_tag)


    def _record_responses(self, current_time):
        # 只记录支座相对位移，桥台和主梁位移通过recorder记录
        self._record_bearing_relative_displacements(current_time)


    def _record_bearing_relative_displacements(self, time):
        """记录每个x坐标处最靠近y=0的支座的盖梁节点和主梁节点之间的相对位移"""
        # 初始化时间步数据（如果是第一次记录）
        if time not in self.bearing_relative_disps:
            self.bearing_relative_disps[time] = []

        # 获取需要记录的支座索引（代表性支座：桥台每个x坐标1个，盖梁每个x坐标2个）
        if not hasattr(self, '_selected_bearing_indices'):
            self._selected_bearing_indices = find_bearings_closest_to_y0(self.model)
            if self._selected_bearing_indices:
                print(f"支座相对位移记录: 从 {len(self.model.bearings['connections'])} 个支座中选择 {len(self._selected_bearing_indices)} 个代表性支座")

        # 只遍历选定的支座连接
        for i in self._selected_bearing_indices:
            deck_node, support_node = self.model.bearings['connections'][i]

            # 获取节点坐标
            x_coord = ops.nodeCoord(deck_node, 1)
            y_coord = ops.nodeCoord(deck_node, 2)

            # 获取节点位移（考虑所有方向）
            deck_disp_x = ops.nodeDisp(deck_node, 1)
            deck_disp_y = ops.nodeDisp(deck_node, 2)
            deck_disp_z = ops.nodeDisp(deck_node, 3)

            support_disp_x = ops.nodeDisp(support_node, 1)
            support_disp_y = ops.nodeDisp(support_node, 2)
            support_disp_z = ops.nodeDisp(support_node, 3)

            # 计算相对位移
            rel_disp_x = deck_disp_x - support_disp_x
            rel_disp_y = deck_disp_y - support_disp_y
            rel_disp_z = deck_disp_z - support_disp_z

            # 获取支座所属的跨号
            span_num = self.model.bearings['spans'][i]

            # 获取支座元素标签
            elem_tag = self.model.bearings['elements'][i]

            # 存储数据
            self.bearing_relative_disps[time].append({
                'bearing_idx': i,
                'span': span_num,
                'x_coord': x_coord,
                'y_coord': y_coord,
                'rel_disp_x': rel_disp_x,
                'rel_disp_y': rel_disp_y,
                'rel_disp_z': rel_disp_z,
                'deck_node': deck_node,
                'support_node': support_node,
                'elem_tag': elem_tag
            })


    def _try_different_algorithms(self, dt_current):
        """尝试不同的算法来解决收敛问题"""
        algorithms = [
            {'name': 'KrylovNewton', 'test': ('NormDispIncr', 1e-4, 100)},
            {'name': 'ModifiedNewton', 'test': ('NormDispIncr', 1e-4, 100), 'params': ('-initial')},
            {'name': 'BFGS', 'test': ('NormDispIncr', 1e-4, 100)},
            {'name': 'Newton', 'test': ('EnergyIncr', 1e-4, 100)},
            {'name': 'BFGS', 'test': ('EnergyIncr', 1e-4, 100)}
        ]

        for alg in algorithms:
            try:
                # 设置算法
                if 'params' in alg:
                    ops.algorithm(alg['name'], *alg['params'])
                else:
                    ops.algorithm(alg['name'])

                # 设置收敛测试
                ops.test(*alg['test'])

                # 尝试分析
                if ops.analyze(1, dt_current) == 0:
                    return True
            except Exception as e:
                print(f"算法 {alg['name']} 失败: {e}")

        return False
